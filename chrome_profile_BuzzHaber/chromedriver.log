[1749088332.649][INFO]: Starting ChromeDriver 113.0.5620.0 (f5cdb01c3f419b5600345dc2d0a48fc8534dc469-refs/heads/main@{#1110125}) on port 50796
[1749088332.650][INFO]: Please see https://chromedriver.chromium.org/security-considerations for suggestions on keeping ChromeDriver safe.
[1749088333.134][INFO]: [1932f306207dab97d2e799a97bf29a09] COMMAND InitSession {
   "capabilities": {
      "alwaysMatch": {
         "browserName": "chrome",
         "goog:chromeOptions": {
            "args": [ "--headless", "--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--disable-gpu-sandbox", "--disable-software-rasterizer", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-notifications", "--disable-popup-blocking", "--disable-web-security", "--disable-features=VizDisplayCompositor,VizServiceDisplayCompositor", "--use-gl=swiftshader", "--disable-accelerated-2d-canvas", "--disable-accelerated-jpeg-decoding", "--disable-accelerated-mjpeg-decode", "--disable-accelerated-video-decode", "--disable-accelerated-video-encode", "--disable-gpu-memory-buffer-compositor-resources", "--disable-gpu-memory-buffer-video-frames", "--window-size=1920,1080", "--user-data-dir=C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\chrome_profile_BuzzHaber", "--remote-debugging-port=50794", "--log-level=3", "--silent" ],
            "binary": "C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome.exe",
            "excludeSwitches": [ "enable-automation" ],
            "extensions": [  ],
            "prefs": {
               "profile.default_content_setting_values.notifications": 2,
               "profile.default_content_settings.popups": 0
            },
            "useAutomationExtension": false
         },
         "pageLoadStrategy": "normal"
      },
      "firstMatch": [ {
      } ]
   }
}
[1749088333.135][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[1749088333.135][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[1749088333.136][INFO]: Populating Preferences file: {
   "alternate_error_pages": {
      "enabled": false
   },
   "autofill": {
      "enabled": false
   },
   "browser": {
      "check_default_browser": false
   },
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "profile": {
      "content_settings": {
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         }
      },
      "default_content_setting_values": {
         "geolocation": 1,
         "notifications": 2
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 0,
         "ppapi-broker": 1
      },
      "password_manager_enabled": false
   },
   "safebrowsing": {
      "enabled": false
   },
   "search": {
      "suggest_enabled": false
   },
   "translate": {
      "enabled": false
   }
}
[1749088333.137][INFO]: Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   }
}
[1749088333.141][INFO]: Launching chrome: "C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe" --allow-pre-commit-input --disable-accelerated-2d-canvas --disable-accelerated-jpeg-decoding --disable-accelerated-mjpeg-decode --disable-accelerated-video-decode --disable-accelerated-video-encode --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-client-side-phishing-detection --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=VizDisplayCompositor,VizServiceDisplayCompositor --disable-gpu --disable-gpu-memory-buffer-compositor-resources --disable-gpu-memory-buffer-video-frames --disable-gpu-sandbox --disable-hang-monitor --disable-images --disable-notifications --disable-plugins --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --disable-software-rasterizer --disable-sync --disable-web-security --enable-blink-features=ShadowDOMV0 --enable-logging --headless --log-level=3 --no-first-run --no-sandbox --no-service-autorun --password-store=basic --remote-debugging-port=50794 --silent --test-type=webdriver --use-gl=swiftshader --use-mock-keychain --user-data-dir="C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_BuzzHaber" --window-size=1920,1080 data:,
[1749088333.147][DEBUG]: DevTools HTTP Request: http://localhost:50794/json/version
[1749088333.456][DEBUG]: DevTools HTTP Response: {

   "Browser": "HeadlessChrome/113.0.5620.0",

   "Protocol-Version": "1.3",

   "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/113.0.5620.0 Safari/537.36",

   "V8-Version": "11.3.9",

   "WebKit-Version": "537.36 (@f5cdb01c3f419b5600345dc2d0a48fc8534dc469)",

   "webSocketDebuggerUrl": "ws://localhost:50794/devtools/browser/3965384b-b4d9-4f53-8ba1-bbb8877186c6"

}


[1749088333.456][DEBUG]: DevTools HTTP Request: http://localhost:50794/json/list
[1749088333.458][DEBUG]: DevTools HTTP Response: [ {

   "description": "",

   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:50794/devtools/page/7686136642C51436FA5F526B0F40A89E",

   "id": "7686136642C51436FA5F526B0F40A89E",

   "title": "",

   "type": "page",

   "url": "data:,",

   "webSocketDebuggerUrl": "ws://localhost:50794/devtools/page/7686136642C51436FA5F526B0F40A89E"

} ]


[1749088333.463][INFO]: resolved localhost to ["::1","127.0.0.1"]
[1749088333.465][DEBUG]: DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}
[1749088333.466][DEBUG]: DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "8917D572044D5AEA6FD8EC5177584427",
      "canAccessOpener": false,
      "targetId": "7686136642C51436FA5F526B0F40A89E",
      "title": "",
      "type": "page",
      "url": "data:,"
   } ]
}
[1749088333.466][DEBUG]: DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "7686136642C51436FA5F526B0F40A89E"
}
[1749088333.467][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "C399B73C3A3A94B3D9FE2C0131050697",
   "targetInfo": {
      "attached": true,
      "browserContextId": "8917D572044D5AEA6FD8EC5177584427",
      "canAccessOpener": false,
      "targetId": "7686136642C51436FA5F526B0F40A89E",
      "title": "",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}
[1749088333.467][DEBUG]: DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "C399B73C3A3A94B3D9FE2C0131050697"
}
[1749088333.467][DEBUG]: DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=3) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[1749088333.467][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=4) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[1749088333.468][DEBUG]: DevTools WebSocket Command: Log.enable (id=5) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
}
[1749088333.468][DEBUG]: DevTools WebSocket Command: Target.setAutoAttach (id=6) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}
[1749088333.468][DEBUG]: DevTools WebSocket Command: Page.enable (id=7) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
}
[1749088333.468][DEBUG]: DevTools WebSocket Command: Page.enable (id=8) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
}
[1749088333.468][DEBUG]: DevTools WebSocket Response: Target.setAutoAttach (id=6) (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
}
[1749088333.527][DEBUG]: DevTools WebSocket Event: Inspector.targetCrashed (session_id=C399B73C3A3A94B3D9FE2C0131050697) 7686136642C51436FA5F526B0F40A89E {
}
[1749088333.536][DEBUG]: DevTools WebSocket Command: Browser.close (id=9) (session_id=) browser {
}
[1749088333.696][INFO]: [1932f306207dab97d2e799a97bf29a09] RESPONSE InitSession ERROR tab crashed
  (Session info: headless chrome=113.0.5620.0)
[1749088333.696][DEBUG]: Log type 'driver' lost 2 entries on destruction
[1749088333.696][DEBUG]: Log type 'browser' lost 0 entries on destruction
