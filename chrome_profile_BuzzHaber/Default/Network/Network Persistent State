{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396154305475845", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396154305642345", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": [], "server": "https://twitter.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396154307351475", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396154312831083", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": [], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396154318997696", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": [], "server": "https://ads-api.x.com", "supports_spdy": true}, {"anonymization": [], "server": "https://x.com", "supports_spdy": true}, {"anonymization": [], "server": "https://abs.twimg.com", "supports_spdy": true}, {"anonymization": [], "server": "https://video.twimg.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 77029}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 49360}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 11535}, "server": "https://r2---sn-u0g3oxu-5tue.gvt1.com"}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}