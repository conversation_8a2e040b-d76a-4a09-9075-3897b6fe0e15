{"account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 113}, "autofill": {"orphan_rows_removed": true}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 1090, "left": 10, "maximized": false, "right": 1930, "top": 10, "work_area_bottom": 728, "work_area_left": 0, "work_area_right": 1366, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21586, "dips_timer_last_update": "*****************", "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.585135, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"consented_to_sync": false, "signin_scoped_device_id": "2a48c504-fcc8-451f-be60-cd013bd2b9f6"}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "media": {"device_id_salt": "038B47065C99A005661CF2C2D86FEA43", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "Q9cKElTmV91Axiux2zvcvkYOJDzLQ4TcU9xzCuMQ+T/w2VB7NqmIapTs0t2Elq7a+GaAcqhmG/nvpYTJcrzurQ=="}, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "privacy_sandbox": {"anti_abuse_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {"https://x.com:443,*": {"last_modified": "*****************", "setting": {"UserDataFieldFilled": true}}}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {"https://x.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://x.com:443,*": {"last_modified": "13393562224434648", "setting": {"lastEngagementTime": 1.3393562224434632e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.6, "rawScore": 6.6}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "top_level_storage_access": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13393562184288970", "default_content_setting_values": {"geolocation": 2, "media_stream": 2, "notifications": 2}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 0, "ppapi-broker": 1}, "exit_type": "Normal", "last_engagement_time": "13393562224434632", "last_time_password_store_metrics_reported": 1749088615.25642, "managed_default_content_settings": {"images": 2, "media_stream": 2}, "managed_user_id": "", "name": "Person 1", "password_manager_enabled": false}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13393562185"}, "search": {"suggest_enabled": false}, "segmentation_platform": {"last_db_compaction_time": "13393468799000000"}, "sessions": {"event_log": [{"crashed": false, "time": "13393562185205011", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393562231674474", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": false}, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "113"}}