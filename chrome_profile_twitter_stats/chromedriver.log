[1749088338.583][INFO]: Starting ChromeDriver 113.0.5620.0 (f5cdb01c3f419b5600345dc2d0a48fc8534dc469-refs/heads/main@{#1110125}) on port 50810
[1749088338.583][INFO]: Please see https://chromedriver.chromium.org/security-considerations for suggestions on keeping ChromeDriver safe.
[1749088339.069][INFO]: [bdeb47e5b5664022f5ac1ed97e1321c8] COMMAND InitSession {
   "capabilities": {
      "alwaysMatch": {
         "browserName": "chrome",
         "goog:chromeOptions": {
            "args": [ "--headless", "--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--disable-gpu-sandbox", "--disable-software-rasterizer", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-notifications", "--disable-popup-blocking", "--disable-web-security", "--disable-features=VizDisplayCompositor,VizServiceDisplayCompositor", "--use-gl=swiftshader", "--disable-accelerated-2d-canvas", "--disable-accelerated-jpeg-decoding", "--disable-accelerated-mjpeg-decode", "--disable-accelerated-video-decode", "--disable-accelerated-video-encode", "--disable-gpu-memory-buffer-compositor-resources", "--disable-gpu-memory-buffer-video-frames", "--window-size=1920,1080", "--user-data-dir=C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\chrome_profile_twitter_stats", "--remote-debugging-port=50808", "--log-level=3", "--silent" ],
            "binary": "C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome.exe",
            "excludeSwitches": [ "enable-automation" ],
            "extensions": [  ],
            "prefs": {
               "profile.default_content_setting_values.notifications": 2,
               "profile.default_content_settings.popups": 0
            },
            "useAutomationExtension": false
         },
         "pageLoadStrategy": "normal"
      },
      "firstMatch": [ {
      } ]
   }
}
[1749088339.069][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[1749088339.069][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[1749088339.070][INFO]: Populating Preferences file: {
   "alternate_error_pages": {
      "enabled": false
   },
   "autofill": {
      "enabled": false
   },
   "browser": {
      "check_default_browser": false
   },
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "profile": {
      "content_settings": {
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         }
      },
      "default_content_setting_values": {
         "geolocation": 1,
         "notifications": 2
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 0,
         "ppapi-broker": 1
      },
      "password_manager_enabled": false
   },
   "safebrowsing": {
      "enabled": false
   },
   "search": {
      "suggest_enabled": false
   },
   "translate": {
      "enabled": false
   }
}
[1749088339.071][INFO]: Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   }
}
[1749088339.075][INFO]: Launching chrome: "C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe" --allow-pre-commit-input --disable-accelerated-2d-canvas --disable-accelerated-jpeg-decoding --disable-accelerated-mjpeg-decode --disable-accelerated-video-decode --disable-accelerated-video-encode --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-client-side-phishing-detection --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=VizDisplayCompositor,VizServiceDisplayCompositor --disable-gpu --disable-gpu-memory-buffer-compositor-resources --disable-gpu-memory-buffer-video-frames --disable-gpu-sandbox --disable-hang-monitor --disable-images --disable-notifications --disable-plugins --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --disable-software-rasterizer --disable-sync --disable-web-security --enable-blink-features=ShadowDOMV0 --enable-logging --headless --log-level=3 --no-first-run --no-sandbox --no-service-autorun --password-store=basic --remote-debugging-port=50808 --silent --test-type=webdriver --use-gl=swiftshader --use-mock-keychain --user-data-dir="C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_twitter_stats" --window-size=1920,1080 data:,
[1749088339.080][DEBUG]: DevTools HTTP Request: http://localhost:50808/json/version
[1749088339.388][DEBUG]: DevTools HTTP Response: {

   "Browser": "HeadlessChrome/113.0.5620.0",

   "Protocol-Version": "1.3",

   "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/113.0.5620.0 Safari/537.36",

   "V8-Version": "11.3.9",

   "WebKit-Version": "537.36 (@f5cdb01c3f419b5600345dc2d0a48fc8534dc469)",

   "webSocketDebuggerUrl": "ws://localhost:50808/devtools/browser/9eb3625e-c3b8-46ba-8929-31bde7d650dc"

}


[1749088339.388][DEBUG]: DevTools HTTP Request: http://localhost:50808/json/list
[1749088339.389][DEBUG]: DevTools HTTP Response: [ {

   "description": "",

   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:50808/devtools/page/87EDFF20217648B8B88ECFD86FFF4EC4",

   "id": "87EDFF20217648B8B88ECFD86FFF4EC4",

   "title": "",

   "type": "page",

   "url": "data:,",

   "webSocketDebuggerUrl": "ws://localhost:50808/devtools/page/87EDFF20217648B8B88ECFD86FFF4EC4"

} ]


[1749088339.395][INFO]: resolved localhost to ["::1","127.0.0.1"]
[1749088339.397][DEBUG]: DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}
[1749088339.398][DEBUG]: DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "015D796C073602C3DE36D0FD2386429B",
      "canAccessOpener": false,
      "targetId": "87EDFF20217648B8B88ECFD86FFF4EC4",
      "title": "",
      "type": "page",
      "url": "data:,"
   } ]
}
[1749088339.398][DEBUG]: DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "87EDFF20217648B8B88ECFD86FFF4EC4"
}
[1749088339.399][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "FA3B90B7A936DF4E7AAD05957F2C38F4",
   "targetInfo": {
      "attached": true,
      "browserContextId": "015D796C073602C3DE36D0FD2386429B",
      "canAccessOpener": false,
      "targetId": "87EDFF20217648B8B88ECFD86FFF4EC4",
      "title": "",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}
[1749088339.399][DEBUG]: DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "FA3B90B7A936DF4E7AAD05957F2C38F4"
}
[1749088339.399][DEBUG]: DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=3) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[1749088339.399][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=4) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[1749088339.399][DEBUG]: DevTools WebSocket Command: Log.enable (id=5) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
}
[1749088339.399][DEBUG]: DevTools WebSocket Command: Target.setAutoAttach (id=6) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}
[1749088339.400][DEBUG]: DevTools WebSocket Command: Page.enable (id=7) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
}
[1749088339.400][DEBUG]: DevTools WebSocket Command: Page.enable (id=8) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
}
[1749088339.400][DEBUG]: DevTools WebSocket Response: Target.setAutoAttach (id=6) (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
}
[1749088339.462][DEBUG]: DevTools WebSocket Event: Inspector.targetCrashed (session_id=FA3B90B7A936DF4E7AAD05957F2C38F4) 87EDFF20217648B8B88ECFD86FFF4EC4 {
}
[1749088339.472][DEBUG]: DevTools WebSocket Command: Browser.close (id=9) (session_id=) browser {
}
[1749088339.654][INFO]: [bdeb47e5b5664022f5ac1ed97e1321c8] RESPONSE InitSession ERROR tab crashed
  (Session info: headless chrome=113.0.5620.0)
[1749088339.654][DEBUG]: Log type 'driver' lost 2 entries on destruction
[1749088339.654][DEBUG]: Log type 'browser' lost 0 entries on destruction
