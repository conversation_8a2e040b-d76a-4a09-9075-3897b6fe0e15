[7448:11632:0605/045121.527:FATAL:chrome_resource_bundle_helper.cc(107)] Check failed: !actual_locale.empty(). Locale could not be found for en-US
Backtrace:
	IsSandboxedProcess [0x00007FFDC079E472+999394]
	IsSandboxedProcess [0x00007FFDC1552242+15367602]
	IsSandboxedProcess [0x00007FFDC082E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC082F8B0+1594400]
	IsSandboxedProcess [0x00007FFDC0743DA8+629016]
	ChromeMain [0x00007FFDBCEE1E4C+3500]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00F8F62+24780930]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00F8C04+24780068]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00F73FF+24773919]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00F76BE+24774622]
	ChromeMain [0x00007FFDBCEE133F+671]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]
Crash keys:
  "reentry_guard_tls_slot" = "unused"
  "variations" = "db59f83a-3f4a17df,8bccc03b-3f4a17df,fabf21f1-3f4a17df,272b4158-3f4a17df,2510663e-73703436,"
  "num-experiments" = "5"
  "switch-40" = "--window-size=1920,1080"
  "switch-39" = "--user-data-dir=C:\Users\<USER>\PycharmProjects\SorcerioM"
  "switch-38" = "--use-mock-keychain"
  "switch-37" = "--use-gl=swiftshader"
  "switch-36" = "--test-type=webdriver"
  "switch-35" = "--silent"
  "switch-34" = "--remote-debugging-port=50708"
  "switch-33" = "--password-store=basic"
  "switch-32" = "--no-service-autorun"
  "switch-31" = "--no-sandbox"
  "switch-30" = "--no-first-run"
  "switch-29" = "--lang=en-US"
  "switch-28" = "--enable-blink-features=ShadowDOMV0"
  "switch-27" = "--disable-web-security"
  "switch-26" = "--disable-sync"
  "switch-25" = "--disable-software-rasterizer"
  "switch-24" = "--disable-renderer-backgrounding"
  "switch-23" = "--disable-prompt-on-repost"
  "switch-22" = "--disable-popup-blocking"
  "switch-21" = "--disable-plugins"
  "switch-20" = "--disable-notifications"
  "switch-19" = "--disable-images"
  "switch-18" = "--disable-hang-monitor"
  "switch-17" = "--disable-gpu-sandbox"
  "switch-16" = "--disable-gpu-memory-buffer-video-frames"
  "switch-15" = "--disable-gpu-memory-buffer-compositor-resources"
  "switch-14" = "--disable-gpu"
  "switch-13" = "--disable-extensions"
  "switch-12" = "--disable-dev-shm-usage"
  "switch-11" = "--disable-default-apps"
  "switch-10" = "--disable-client-side-phishing-detection"
  "switch-9" = "--disable-backgrounding-occluded-windows"
  "switch-8" = "--disable-background-timer-throttling"
  "switch-7" = "--disable-background-networking"
  "switch-6" = "--disable-accelerated-video-encode"
  "switch-5" = "--disable-accelerated-video-decode"
  "switch-4" = "--disable-accelerated-mjpeg-decode"
  "switch-3" = "--disable-accelerated-jpeg-decoding"
  "switch-2" = "--disable-accelerated-2d-canvas"
  "switch-1" = "--allow-pre-commit-input"
  "num-switches" = "43"
  "commandline-disabled-feature-2" = "VizServiceDisplayCompositor"
  "commandline-disabled-feature-1" = "VizDisplayCompositor"

