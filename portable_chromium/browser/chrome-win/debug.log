[0605/044758.338:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044758.338:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044758.347:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044758.354:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044758.372:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044758.434:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044758.435:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044758.518:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044804.272:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044804.273:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044804.286:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044804.290:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044804.302:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044804.368:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044804.368:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044804.468:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044810.238:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044810.239:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044810.251:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044810.267:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044810.259:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044810.333:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044810.333:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044815.937:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044815.938:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044815.955:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044815.966:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044815.955:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044816.033:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044816.034:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044816.141:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0FEE472+999394]
	IsSandboxedProcess [0x00007FFDC1DA2242+15367602]
	IsSandboxedProcess [0x00007FFDC107E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18CCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18CC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0948374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC094756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09476BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0EF9BEC+30747916]
	ChromeMain [0x00007FFDBD731316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044822.018:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044822.019:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044822.025:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044822.026:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044822.046:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044822.110:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044822.111:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044822.214:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044827.896:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044827.897:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044827.909:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044827.925:ERROR:viz_main_impl.cc(186)] Exiting GPU process due to errors during initialization
[0605/044827.920:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044827.991:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044827.992:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044828.056:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC113E472+999394]
	IsSandboxedProcess [0x00007FFDC1EF2242+15367602]
	IsSandboxedProcess [0x00007FFDC11CE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC1A1CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC1A1C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A98374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A9756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0A976BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC1049BEC+30747916]
	ChromeMain [0x00007FFDBD881316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044923.403:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044923.403:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044923.419:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044923.425:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044923.581:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044929.165:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044929.165:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044929.178:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044929.184:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044929.357:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044935.158:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044935.158:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044935.160:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044935.172:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044935.309:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044941.069:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044941.070:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044941.065:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044941.074:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044941.216:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC0EBE472+999394]
	IsSandboxedProcess [0x00007FFDC1C72242+15367602]
	IsSandboxedProcess [0x00007FFDC0F4E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC179CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC179C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0818374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC081756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC08176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0DC9BEC+30747916]
	ChromeMain [0x00007FFDBD601316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044947.090:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044947.091:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044947.103:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044947.104:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044947.287:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044953.078:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/044953.078:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/044953.089:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044953.100:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/044953.232:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC100E472+999394]
	IsSandboxedProcess [0x00007FFDC1DC2242+15367602]
	IsSandboxedProcess [0x00007FFDC109E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC18ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC18EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0968374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC096756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC09676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0F19BEC+30747916]
	ChromeMain [0x00007FFDBD751316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045149.636:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045149.636:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045149.644:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045149.657:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045149.783:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045155.547:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045155.547:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045155.568:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045155.569:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045155.758:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC064E472+999394]
	IsSandboxedProcess [0x00007FFDC1402242+15367602]
	IsSandboxedProcess [0x00007FFDC06DE7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F2CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F2C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA8374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDBFFA76BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0559BEC+30747916]
	ChromeMain [0x00007FFDBCD91316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045201.555:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045201.555:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045201.564:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045201.575:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045201.704:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045207.341:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045207.341:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045207.356:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045207.361:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045207.553:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC06BE472+999394]
	IsSandboxedProcess [0x00007FFDC1472242+15367602]
	IsSandboxedProcess [0x00007FFDC074E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC0F9CAAC+10298908]
	IsSandboxedProcess [0x00007FFDC0F9C8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0018374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC001756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC00176BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC05C9BEC+30747916]
	ChromeMain [0x00007FFDBCE01316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045213.432:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045213.432:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045213.439:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045213.450:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045213.588:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045219.345:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045219.346:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045219.346:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045219.351:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045219.529:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC080E472+999394]
	IsSandboxedProcess [0x00007FFDC15C2242+15367602]
	IsSandboxedProcess [0x00007FFDC089E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC10ECAAC+10298908]
	IsSandboxedProcess [0x00007FFDC10EC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0168374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC016756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC01676BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC0719BEC+30747916]
	ChromeMain [0x00007FFDBCF51316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045349.261:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045349.262:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045349.269:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045349.276:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045349.422:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045355.126:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045355.127:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045355.135:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045355.140:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045355.309:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045400.938:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045400.939:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045400.952:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045400.951:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045401.129:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045406.798:WARNING:resource_bundle.cc(451)] locale_file_path.empty() for locale 
[0605/045406.798:ERROR:resource_bundle.cc(992)] Failed to load C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\resources.pak
Some features may not be available.
[0605/045406.808:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045406.814:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

[0605/045406.967:FATAL:v8_initializer.cc(518)] Error loading V8 startup snapshot file
Backtrace:
	IsSandboxedProcess [0x00007FFDC2AFE472+999394]
	IsSandboxedProcess [0x00007FFDC38B2242+15367602]
	IsSandboxedProcess [0x00007FFDC2B8E7A2+1590034]
	IsSandboxedProcess [0x00007FFDC33DCAAC+10298908]
	IsSandboxedProcess [0x00007FFDC33DC8DE+10298446]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2458374+24777876]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC245756C+24774284]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC24576BE+24774622]
	CrashForExceptionInNonABICompliantCodeRange [0x00007FFDC2A09BEC+30747916]
	ChromeMain [0x00007FFDBF241316+630]
	GetPakFileHashes [0x00007FF7133F29FF+6655]
	GetPakFileHashes [0x00007FF7133F1A96+2710]
	GetHandleVerifier [0x00007FF713565F32+1051794]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

